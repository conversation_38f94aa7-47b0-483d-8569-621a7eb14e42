import { _decorator, Component, Node } from 'cc';
import { GameData, BirdType, GameMode } from './GameData';
import { ItemManager, ItemType } from './ItemManager';
const { ccclass, property } = _decorator;

/**
 * Bug修复测试类
 * 测试金币四舍五入和复活双倍金币卡补偿功能
 */
@ccclass('BugFixTest')
export class BugFixTest extends Component {

    start() {
        console.log("=== Bug修复测试开始 ===");
        this.runAllTests();
    }

    private runAllTests() {
        this.testCoinRounding();
        this.testReviveDoubleCoinCompensation();
    }

    /**
     * 测试金币四舍五入功能
     */
    private testCoinRounding() {
        console.log("\n--- 测试1: 金币四舍五入测试 ---");
        
        // 重置状态
        GameData.resetSessionCoins();
        GameData.clearLuckyDiceMultiplier();
        ItemManager.deactivateItemEffect(ItemType.DOUBLE_COIN);
        ItemManager.deactivateItemEffect(ItemType.LUCKY_DICE);
        
        // 设置测试环境：金色小鸟 + 标准关卡 + 幸运骰子(1倍)
        GameData.setCurrentGameMode(GameMode.NORMAL_STANDARD);
        GameData.setSelectedBirdType(BirdType.GOLD);
        ItemManager.setItemCount(ItemType.LUCKY_DICE, 1);
        ItemManager.activateItemEffect(ItemType.LUCKY_DICE);
        GameData.setLuckyDiceMultiplier(1);
        
        // 测试案例1: 收集3个金币
        console.log("\n测试案例1: 收集3个金币");
        GameData.resetSessionCoins();
        GameData.addCoin(3);
        
        const sessionCoins1 = GameData.getSessionCoins();
        const finalCoins1 = GameData.getFinalSessionCoins();
        const multiplier1 = GameData.getCurrentCoinMultiplier();
        
        console.log(`原始金币: ${sessionCoins1}`);
        console.log(`倍率: ${multiplier1} (1.2 × 1 × 1.5 × 1)`);
        console.log(`计算结果: ${sessionCoins1 * multiplier1}`);
        console.log(`最终金币: ${finalCoins1}`);
        
        // 3 * 1.8 = 5.4，四舍五入应该是5
        if (sessionCoins1 === 3 && finalCoins1 === 5 && Math.abs(multiplier1 - 1.8) < 0.001) {
            console.log("✅ 案例1通过 (5.4 → 5)");
        } else {
            console.error(`❌ 案例1失败: 期望5, 实际${finalCoins1}`);
        }
        
        // 测试案例2: 收集7个金币
        console.log("\n测试案例2: 收集7个金币");
        GameData.resetSessionCoins();
        GameData.addCoin(7);
        
        const sessionCoins2 = GameData.getSessionCoins();
        const finalCoins2 = GameData.getFinalSessionCoins();
        
        console.log(`原始金币: ${sessionCoins2}`);
        console.log(`计算结果: ${sessionCoins2 * multiplier1}`);
        console.log(`最终金币: ${finalCoins2}`);
        
        // 7 * 1.8 = 12.6，四舍五入应该是13
        if (sessionCoins2 === 7 && finalCoins2 === 13) {
            console.log("✅ 案例2通过 (12.6 → 13)");
        } else {
            console.error(`❌ 案例2失败: 期望13, 实际${finalCoins2}`);
        }
        
        // 测试案例3: 收集5个金币
        console.log("\n测试案例3: 收集5个金币");
        GameData.resetSessionCoins();
        GameData.addCoin(5);
        
        const sessionCoins3 = GameData.getSessionCoins();
        const finalCoins3 = GameData.getFinalSessionCoins();
        
        console.log(`原始金币: ${sessionCoins3}`);
        console.log(`计算结果: ${sessionCoins3 * multiplier1}`);
        console.log(`最终金币: ${finalCoins3}`);
        
        // 5 * 1.8 = 9.0，应该是9
        if (sessionCoins3 === 5 && finalCoins3 === 9) {
            console.log("✅ 案例3通过 (9.0 → 9)");
        } else {
            console.error(`❌ 案例3失败: 期望9, 实际${finalCoins3}`);
        }
        
        console.log("✅ 金币四舍五入测试完成");
    }

    /**
     * 测试复活双倍金币卡补偿功能
     */
    private testReviveDoubleCoinCompensation() {
        console.log("\n--- 测试2: 复活双倍金币卡补偿测试 ---");
        
        // 重置状态
        GameData.resetSessionCoins();
        GameData.resetSessionReviveCount();
        GameData.clearReviveState();
        ItemManager.deactivateItemEffect(ItemType.DOUBLE_COIN);
        
        // 设置初始道具数量
        ItemManager.setItemCount(ItemType.DOUBLE_COIN, 2);
        ItemManager.setItemCount(ItemType.REVIVE_COIN, 1);
        
        console.log("\n=== 模拟游戏流程 ===");
        
        // 第一步：激活双倍金币卡
        console.log("\n1. 激活双倍金币卡");
        ItemManager.activateItemEffect(ItemType.DOUBLE_COIN);
        console.log(`双倍金币卡数量: ${ItemManager.getItemCount(ItemType.DOUBLE_COIN)}`);
        console.log(`双倍金币卡激活: ${ItemManager.isItemActive(ItemType.DOUBLE_COIN)}`);
        
        // 第二步：模拟第一次游戏结束（消耗双倍金币卡）
        console.log("\n2. 第一次游戏结束");
        ItemManager.consumeActiveItems();
        console.log(`双倍金币卡数量: ${ItemManager.getItemCount(ItemType.DOUBLE_COIN)}`);
        console.log(`双倍金币卡激活: ${ItemManager.isItemActive(ItemType.DOUBLE_COIN)}`);
        
        // 验证第一次消耗
        const countAfterFirstDeath = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
        const activeAfterFirstDeath = ItemManager.isItemActive(ItemType.DOUBLE_COIN);
        
        if (countAfterFirstDeath === 1 && activeAfterFirstDeath === true) {
            console.log("✅ 第一次死亡正确消耗双倍金币卡");
        } else {
            console.error("❌ 第一次死亡消耗异常");
        }
        
        // 第三步：模拟复活（应该补偿双倍金币卡）
        console.log("\n3. 使用复活币复活");
        
        // 模拟复活逻辑中的补偿部分
        if (ItemManager.isItemActive(ItemType.DOUBLE_COIN)) {
            const currentDoubleCoinCount = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
            ItemManager.setItemCount(ItemType.DOUBLE_COIN, currentDoubleCoinCount + 1);
            console.log(`复活补偿：双倍金币卡数量 +1，当前数量: ${currentDoubleCoinCount + 1}`);
        }
        
        console.log(`复活后双倍金币卡数量: ${ItemManager.getItemCount(ItemType.DOUBLE_COIN)}`);
        console.log(`复活后双倍金币卡激活: ${ItemManager.isItemActive(ItemType.DOUBLE_COIN)}`);
        
        // 验证复活补偿
        const countAfterRevive = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
        const activeAfterRevive = ItemManager.isItemActive(ItemType.DOUBLE_COIN);
        
        if (countAfterRevive === 2 && activeAfterRevive === true) {
            console.log("✅ 复活正确补偿双倍金币卡");
        } else {
            console.error("❌ 复活补偿异常");
        }
        
        // 第四步：模拟第二次游戏结束（再次消耗双倍金币卡）
        console.log("\n4. 第二次游戏结束");
        ItemManager.consumeActiveItems();
        console.log(`双倍金币卡数量: ${ItemManager.getItemCount(ItemType.DOUBLE_COIN)}`);
        console.log(`双倍金币卡激活: ${ItemManager.isItemActive(ItemType.DOUBLE_COIN)}`);
        
        // 验证第二次消耗
        const countAfterSecondDeath = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
        const activeAfterSecondDeath = ItemManager.isItemActive(ItemType.DOUBLE_COIN);
        
        if (countAfterSecondDeath === 1 && activeAfterSecondDeath === true) {
            console.log("✅ 第二次死亡正确消耗双倍金币卡");
        } else {
            console.error("❌ 第二次死亡消耗异常");
        }
        
        // 总结测试结果
        console.log("\n=== 测试总结 ===");
        console.log("预期流程：");
        console.log("- 初始：2张双倍金币卡");
        console.log("- 第一次死亡：消耗1张，剩余1张");
        console.log("- 复活：补偿1张，恢复到2张");
        console.log("- 第二次死亡：消耗1张，剩余1张");
        console.log("- 结果：一局游戏只消耗了1张双倍金币卡");
        
        if (countAfterSecondDeath === 1) {
            console.log("✅ 复活双倍金币卡补偿测试通过");
        } else {
            console.error("❌ 复活双倍金币卡补偿测试失败");
        }
        
        console.log("=== Bug修复测试结束 ===");
    }

    /**
     * 静态方法，可以从控制台手动调用
     */
    public static runManualTest() {
        console.log("=== 手动运行Bug修复测试 ===");
        const testInstance = new BugFixTest();
        testInstance.runAllTests();
    }
}
