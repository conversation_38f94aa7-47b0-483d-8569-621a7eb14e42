import { _decorator, Component, Node } from 'cc';
import { GameData } from './GameData';
const { ccclass, property } = _decorator;

// 道具类型枚举
export enum ItemType {
    DOUBLE_COIN = 0,  // 双倍金币卡
    MAGNET = 1,       // 吸金石
    REVIVE_COIN = 2,  // 复活币
    LUCKY_DICE = 3,   // 幸运骰子
    // 可以在这里添加更多道具类型
}

// 道具数据接口
interface ItemData {
    type: ItemType;
    count: number;
    price: number;
    name: string;
    description: string;
}

/**
 * 道具管理器
 * 负责管理所有道具的购买、使用、状态等
 */
@ccclass('ItemManager')
export class ItemManager extends Component {

    // 存储键前缀
    private static readonly ITEM_COUNT_PREFIX = "Item_Count_";
    private static readonly ITEM_ACTIVE_PREFIX = "Item_Active_";

    // 道具配置
    private static readonly ITEM_CONFIGS: {[key: number]: ItemData} = {
        [ItemType.DOUBLE_COIN]: {
            type: ItemType.DOUBLE_COIN,
            count: 0,
            price: 50,
            name: "双倍金币卡",
            description: "使用后本局获得的金币翻倍"
        },
        [ItemType.MAGNET]: {
            type: ItemType.MAGNET,
            count: 0,
            price: 50,
            name: "吸金石",
            description: "购买后永久生效，自动吸取半径300范围内的金币"
        },
        [ItemType.REVIVE_COIN]: {
            type: ItemType.REVIVE_COIN,
            count: 0,
            price: 10,
            name: "复活币",
            description: "游戏结束时可以使用复活币重新开始，保留当前分数和金币"
        },
        [ItemType.LUCKY_DICE]: {
            type: ItemType.LUCKY_DICE,
            count: 0,
            price: 50,
            name: "幸运骰子",
            description: "永久道具，使用后本局金币获得1-6倍随机倍率"
        }
    };

    // 单例实例
    private static _instance: ItemManager = null;

    // 当前激活的道具效果
    private static _activeCoinMultiplier: number = 1; // 金币倍数
    private static _magnetActive: boolean = false; // 吸金石是否激活
    private static _luckyDiceActive: boolean = false; // 幸运骰子是否激活

    onLoad() {
        ItemManager._instance = this;
        console.log("ItemManager 初始化完成");
    }

    onDestroy() {
        if (ItemManager._instance === this) {
            ItemManager._instance = null;
        }
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): ItemManager | null {
        return ItemManager._instance;
    }

    /**
     * 获取道具数量
     */
    public static getItemCount(itemType: ItemType): number {
        const key = this.ITEM_COUNT_PREFIX + itemType;
        const count = localStorage.getItem(key);
        return count ? parseInt(count) : 0;
    }

    /**
     * 设置道具数量
     */
    public static setItemCount(itemType: ItemType, count: number): void {
        const key = this.ITEM_COUNT_PREFIX + itemType;
        localStorage.setItem(key, count.toString());
        console.log(`设置道具 ${this.getItemName(itemType)} 数量为: ${count}`);
    }

    /**
     * 购买道具
     */
    public static purchaseItem(itemType: ItemType): boolean {
        const config = this.ITEM_CONFIGS[itemType];
        if (!config) {
            console.error(`未找到道具配置: ${itemType}`);
            return false;
        }

        // 对于永久道具（吸金石和幸运骰子），检查是否已经拥有
        if (itemType === ItemType.MAGNET || itemType === ItemType.LUCKY_DICE) {
            const currentCount = this.getItemCount(itemType);
            if (currentCount > 0) {
                console.log(`${config.name} 是永久道具，已经拥有，无需重复购买`);
                return false;
            }
        }

        const totalCoins = GameData.getTotalCoins();
        if (totalCoins >= config.price) {
            // 扣除金币
            const newTotal = totalCoins - config.price;
            localStorage.setItem("TotalCoins", newTotal.toString());

            // 增加道具数量
            const currentCount = this.getItemCount(itemType);
            this.setItemCount(itemType, currentCount + 1);

            console.log(`成功购买 ${config.name}，花费 ${config.price} 金币，剩余 ${newTotal} 金币`);
            return true;
        } else {
            console.log(`金币不足，无法购买 ${config.name}，需要 ${config.price} 金币，当前只有 ${totalCoins} 金币`);
            return false;
        }
    }

    /**
     * 使用道具
     */
    public static useItem(itemType: ItemType): boolean {
        const currentCount = this.getItemCount(itemType);
        if (currentCount <= 0) {
            console.log(`道具 ${this.getItemName(itemType)} 数量不足`);
            return false;
        }

        // 减少道具数量
        this.setItemCount(itemType, currentCount - 1);

        // 激活道具效果
        this.activateItemEffect(itemType);

        console.log(`使用了道具 ${this.getItemName(itemType)}`);
        return true;
    }

    /**
     * 激活道具效果（公开方法）
     */
    public static activateItemEffect(itemType: ItemType): void {
        const key = this.ITEM_ACTIVE_PREFIX + itemType;
        localStorage.setItem(key, "true");

        switch (itemType) {
            case ItemType.DOUBLE_COIN:
                this._activeCoinMultiplier = 2;
                console.log("双倍金币卡已激活，本局金币将翻倍");
                break;
            case ItemType.MAGNET:
                this._magnetActive = true;
                console.log("吸金石已激活，金币收集距离扩大到300");
                break;
            case ItemType.LUCKY_DICE:
                this._luckyDiceActive = true;
                console.log("幸运骰子已激活，本局金币将获得随机倍率");
                break;
        }
    }

    /**
     * 取消道具效果
     */
    public static deactivateItemEffect(itemType: ItemType): void {
        const key = this.ITEM_ACTIVE_PREFIX + itemType;
        localStorage.removeItem(key);

        switch (itemType) {
            case ItemType.DOUBLE_COIN:
                this._activeCoinMultiplier = 1;
                console.log("双倍金币卡效果已取消");
                break;
            case ItemType.MAGNET:
                this._magnetActive = false;
                console.log("吸金石效果已取消，金币收集距离恢复到50");
                break;
            case ItemType.LUCKY_DICE:
                this._luckyDiceActive = false;
                console.log("幸运骰子效果已取消");
                break;
        }
    }

    /**
     * 检查道具是否激活
     */
    public static isItemActive(itemType: ItemType): boolean {
        const key = this.ITEM_ACTIVE_PREFIX + itemType;
        return localStorage.getItem(key) === "true";
    }

    /**
     * 获取当前金币倍数
     */
    public static getCoinMultiplier(): number {
        return this._activeCoinMultiplier;
    }

    /**
     * 检查吸金石是否激活
     */
    public static isMagnetActive(): boolean {
        return this._magnetActive;
    }

    /**
     * 检查幸运骰子是否激活
     */
    public static isLuckyDiceActive(): boolean {
        return this._luckyDiceActive;
    }

    /**
     * 获取当前金币收集距离
     */
    public static getCoinCollectionDistance(): number {
        return this._magnetActive ? 300 : 50;
    }

    /**
     * 重置所有道具效果（游戏开始时调用）
     */
    public static resetAllEffects(): void {
        // 重置金币倍数、吸金石和幸运骰子状态
        this._activeCoinMultiplier = 1;
        this._magnetActive = false;
        this._luckyDiceActive = false;

        // 检查是否有激活的双倍金币卡效果且有足够数量
        if (this.isItemActive(ItemType.DOUBLE_COIN)) {
            const currentCount = this.getItemCount(ItemType.DOUBLE_COIN);
            if (currentCount > 0) {
                this._activeCoinMultiplier = 2;
                console.log("检测到激活的双倍金币卡，本局金币将翻倍");
            } else {
                // 如果没有道具了，自动取消激活状态
                this.deactivateItemEffect(ItemType.DOUBLE_COIN);
                console.log("双倍金币卡数量为0，自动取消激活状态");
            }
        }

        // 检查是否有激活的吸金石效果且有足够数量
        if (this.isItemActive(ItemType.MAGNET)) {
            const currentCount = this.getItemCount(ItemType.MAGNET);
            if (currentCount > 0) {
                this._magnetActive = true;
                console.log("检测到激活的吸金石，本局金币收集距离扩大到300");
            } else {
                // 如果没有道具了，自动取消激活状态
                this.deactivateItemEffect(ItemType.MAGNET);
                console.log("吸金石数量为0，自动取消激活状态");
            }
        }

        // 检查是否有激活的幸运骰子效果且有足够数量
        if (this.isItemActive(ItemType.LUCKY_DICE)) {
            const currentCount = this.getItemCount(ItemType.LUCKY_DICE);
            if (currentCount > 0) {
                this._luckyDiceActive = true;
                console.log("检测到激活的幸运骰子，本局金币将获得随机倍率");
            } else {
                // 如果没有道具了，自动取消激活状态
                this.deactivateItemEffect(ItemType.LUCKY_DICE);
                console.log("幸运骰子数量为0，自动取消激活状态");
            }
        }
    }

    /**
     * 游戏结束时消耗道具并管理效果
     */
    public static consumeActiveItems(): void {
        // 如果双倍金币卡是激活状态，消耗一个
        if (this.isItemActive(ItemType.DOUBLE_COIN)) {
            const currentCount = this.getItemCount(ItemType.DOUBLE_COIN);
            if (currentCount > 0) {
                const newCount = currentCount - 1;
                this.setItemCount(ItemType.DOUBLE_COIN, newCount);
                console.log(`消耗了1个双倍金币卡，剩余数量: ${newCount}`);

                // 如果还有剩余数量，保持激活状态；否则取消激活
                if (newCount <= 0) {
                    this.deactivateItemEffect(ItemType.DOUBLE_COIN);
                    console.log("双倍金币卡数量为0，取消激活状态");
                } else {
                    console.log("双倍金币卡仍有剩余，保持激活状态");
                }
            } else {
                // 如果数量为0但仍处于激活状态，取消激活
                this.deactivateItemEffect(ItemType.DOUBLE_COIN);
                console.log("双倍金币卡数量为0，取消激活状态");
            }
        }

        // 吸金石是永久道具，不消耗
        if (this.isItemActive(ItemType.MAGNET)) {
            console.log("吸金石是永久道具，保持激活状态");
        }

        // 幸运骰子是永久道具，不消耗
        if (this.isItemActive(ItemType.LUCKY_DICE)) {
            console.log("幸运骰子是永久道具，保持激活状态");
        }

        // 重置内存中的状态，下次游戏开始时会重新检查
        this._activeCoinMultiplier = 1;
        this._magnetActive = false;
        this._luckyDiceActive = false;
    }

    /**
     * 游戏结束时清理道具效果（不消耗道具）
     */
    public static clearGameEffects(): void {
        // 清除所有激活的道具效果
        this.deactivateItemEffect(ItemType.DOUBLE_COIN);
        this.deactivateItemEffect(ItemType.MAGNET);
        this.deactivateItemEffect(ItemType.LUCKY_DICE);
    }

    /**
     * 获取道具名称
     */
    public static getItemName(itemType: ItemType): string {
        const config = this.ITEM_CONFIGS[itemType];
        return config ? config.name : "未知道具";
    }

    /**
     * 获取道具价格
     */
    public static getItemPrice(itemType: ItemType): number {
        const config = this.ITEM_CONFIGS[itemType];
        return config ? config.price : 0;
    }

    /**
     * 获取道具描述
     */
    public static getItemDescription(itemType: ItemType): string {
        const config = this.ITEM_CONFIGS[itemType];
        return config ? config.description : "无描述";
    }

    /**
     * 生成幸运骰子随机倍率
     * 概率分布：69%概率1倍，16%概率2倍，8%概率3倍，4%概率4倍，2%概率5倍，1%概率6倍
     */
    public static generateLuckyDiceMultiplier(): number {
        const random = Math.random() * 100; // 0-100的随机数

        if (random < 69) {
            return 1; // 69%概率
        } else if (random < 85) { // 69 + 16 = 85
            return 2; // 16%概率
        } else if (random < 93) { // 85 + 8 = 93
            return 3; // 8%概率
        } else if (random < 97) { // 93 + 4 = 97
            return 4; // 4%概率
        } else if (random < 99) { // 97 + 2 = 99
            return 5; // 2%概率
        } else {
            return 6; // 1%概率
        }
    }
}
